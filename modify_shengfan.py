#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
上位机程序修改工具 - 将学长的MQTT Dashboard配置改为OneNET配置
作者: AI助手
功能: 修改ShengFan.exe中的服务器地址和主题
"""

import os
import shutil
import sys

def backup_file(source_path):
    """备份原始文件"""
    backup_path = source_path.replace('.exe', '_backup.exe')
    try:
        shutil.copy2(source_path, backup_path)
        print(f"✅ 备份完成: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False

def modify_mqtt_config(file_path):
    """修改MQTT配置"""
    try:
        # 读取文件
        with open(file_path, 'rb') as f:
            data = f.read()
        
        print(f"📁 文件大小: {len(data)} 字节")
        
        # 定义替换规则
        replacements = [
            # 服务器地址替换 (保持相同长度很重要)
            (b'broker.mqttdashboard.com', b'*************\x00\x00\x00\x00\x00\x00\x00\x00\x00'),  # OneNET MQTT服务器
            
            # 主题替换 (保持相同长度)
            (b'nitamade/jiushe/ge/shabi', b'$dp\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00'),  # OneNET数据点主题
        ]
        
        # 执行替换
        modified_data = data
        replacement_count = 0
        
        for old_bytes, new_bytes in replacements:
            if old_bytes in modified_data:
                modified_data = modified_data.replace(old_bytes, new_bytes)
                replacement_count += 1
                print(f"✅ 替换成功: {old_bytes.decode('utf-8', errors='ignore')} -> {new_bytes.decode('utf-8', errors='ignore').rstrip('\x00')}")
            else:
                print(f"⚠️  未找到: {old_bytes.decode('utf-8', errors='ignore')}")
        
        if replacement_count > 0:
            # 写入修改后的文件
            output_path = file_path.replace('.exe', '_onenet.exe')
            with open(output_path, 'wb') as f:
                f.write(modified_data)
            print(f"✅ 修改完成: {output_path}")
            print(f"📊 总共替换了 {replacement_count} 处配置")
            return output_path
        else:
            print("❌ 没有找到需要替换的配置")
            return None
            
    except Exception as e:
        print(f"❌ 修改失败: {e}")
        return None

def create_config_info():
    """创建配置说明文件"""
    config_info = """
# OneNET配置修改说明

## 修改内容：
1. MQTT服务器地址: broker.mqttdashboard.com -> *************
2. MQTT主题: nitamade/jiushe/ge/shabi -> $dp

## 注意事项：
1. 端口需要从1883改为6002 (这个需要在STM32代码中修改)
2. 需要配置OneNET的认证信息
3. 数据格式需要符合OneNET的JSON格式

## 下一步：
1. 测试修改后的程序是否能正常运行
2. 配置OneNET的产品ID、设备ID和访问密钥
3. 确保STM32发送的数据格式正确

## 如果遇到问题：
1. 使用备份文件恢复原程序
2. 检查OneNET平台配置
3. 验证网络连接
"""
    
    with open('OneNET_Config_Info.txt', 'w', encoding='utf-8') as f:
        f.write(config_info)
    print("📝 配置说明文件已创建: OneNET_Config_Info.txt")

def main():
    """主函数"""
    print("🚀 上位机程序OneNET适配工具")
    print("=" * 50)
    
    # 文件路径
    exe_path = r"C:\Users\<USER>\Desktop\智能电表\package\package\ShengFan.exe"
    
    # 检查文件是否存在
    if not os.path.exists(exe_path):
        print(f"❌ 文件不存在: {exe_path}")
        return
    
    # 备份原文件
    if not backup_file(exe_path):
        return
    
    # 修改配置
    modified_path = modify_mqtt_config(exe_path)
    
    if modified_path:
        print("\n🎉 修改完成！")
        print(f"📁 原文件: {exe_path}")
        print(f"📁 备份文件: {exe_path.replace('.exe', '_backup.exe')}")
        print(f"📁 修改后文件: {modified_path}")
        
        # 创建配置说明
        create_config_info()
        
        print("\n⚠️  重要提醒:")
        print("1. 请先测试修改后的程序是否能正常启动")
        print("2. 需要在OneNET平台配置相应的产品和设备")
        print("3. 确保STM32代码中的端口改为6002")
        print("4. 如有问题，使用备份文件恢复")
        
    else:
        print("❌ 修改失败，请检查文件或联系技术支持")

if __name__ == "__main__":
    main()
