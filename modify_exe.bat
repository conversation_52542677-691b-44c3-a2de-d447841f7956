@echo off
chcp 65001 >nul
echo 🚀 上位机程序OneNET适配工具
echo ==================================================

set "SOURCE=C:\Users\<USER>\Desktop\智能电表\package\package\ShengFan.exe"
set "BACKUP=C:\Users\<USER>\Desktop\智能电表\package\package\ShengFan_backup.exe"
set "OUTPUT=C:\Users\<USER>\Desktop\智能电表\package\package\ShengFan_onenet.exe"

echo 📁 检查源文件...
if not exist "%SOURCE%" (
    echo ❌ 源文件不存在: %SOURCE%
    pause
    exit /b 1
)

echo ✅ 找到源文件: %SOURCE%

echo 📁 创建备份...
copy "%SOURCE%" "%BACKUP%" >nul
if errorlevel 1 (
    echo ❌ 备份失败
    pause
    exit /b 1
)
echo ✅ 备份完成: %BACKUP%

echo 📁 复制文件用于修改...
copy "%SOURCE%" "%OUTPUT%" >nul
if errorlevel 1 (
    echo ❌ 复制失败
    pause
    exit /b 1
)

echo ✅ 文件准备完成
echo.
echo ⚠️  重要说明:
echo 1. 已创建备份文件: ShengFan_backup.exe
echo 2. 已创建修改用文件: ShengFan_onenet.exe
echo 3. 需要使用十六进制编辑器手动修改以下内容:
echo.
echo 🔧 需要修改的内容:
echo    原服务器: broker.mqttdashboard.com
echo    新服务器: *************
echo.
echo    原主题: nitamade/jiushe/ge/shabi  
echo    新主题: $dp
echo.
echo 📝 建议使用HxD十六进制编辑器进行修改
echo    下载地址: https://mh-nexus.de/en/hxd/
echo.

echo 📋 创建修改指南...
(
echo # 上位机程序修改指南
echo.
echo ## 步骤1: 下载十六进制编辑器
echo 推荐使用HxD: https://mh-nexus.de/en/hxd/
echo.
echo ## 步骤2: 打开文件
echo 使用HxD打开: ShengFan_onenet.exe
echo.
echo ## 步骤3: 搜索和替换
echo.
echo ### 替换服务器地址:
echo 搜索 ^(文本^): broker.mqttdashboard.com
echo 替换为: *************
echo 注意: 新地址较短，需要用空字节^(00^)填充剩余空间
echo.
echo ### 替换MQTT主题:
echo 搜索 ^(文本^): nitamade/jiushe/ge/shabi
echo 替换为: $dp
echo 注意: 新主题较短，需要用空字节^(00^)填充剩余空间
echo.
echo ## 步骤4: 保存文件
echo 保存修改后的文件
echo.
echo ## 步骤5: 测试
echo 运行修改后的程序，检查是否正常启动
echo.
echo ## 您的OneNET配置信息:
echo - 产品ID: cF16DWy2B8
echo - 设备ID: stm32f103  
echo - 访问密钥: QU9uYW44bG5pcG1NVkhLWmNPUnNGZXVVcHhDZEtQMkM=
echo - MQTT服务器: *************
echo - MQTT端口: 6002 ^(需要在STM32代码中修改^)
echo.
echo ## 注意事项:
echo 1. 确保STM32代码中端口改为6002
echo 2. 数据格式需要符合OneNET的JSON格式
echo 3. 如有问题，使用备份文件恢复
) > "修改指南.txt"

echo ✅ 修改指南已创建: 修改指南.txt
echo.
echo 🎉 准备工作完成！
echo 📖 请查看"修改指南.txt"了解详细步骤
echo.
pause
