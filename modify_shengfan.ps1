# PowerShell脚本：修改上位机程序的MQTT配置
# 将学长的MQTT Dashboard配置改为OneNET配置

Write-Host "🚀 上位机程序OneNET适配工具" -ForegroundColor Green
Write-Host "=" * 50

# 文件路径
$exePath = "C:\Users\<USER>\Desktop\智能电表\package\package\ShengFan.exe"
$backupPath = "C:\Users\<USER>\Desktop\智能电表\package\package\ShengFan_backup.exe"
$outputPath = "C:\Users\<USER>\Desktop\智能电表\package\package\ShengFan_onenet.exe"

# 检查文件是否存在
if (-not (Test-Path $exePath)) {
    Write-Host "❌ 文件不存在: $exePath" -ForegroundColor Red
    exit
}

Write-Host "📁 找到目标文件: $exePath" -ForegroundColor Green

# 备份原文件
try {
    Copy-Item $exePath $backupPath -Force
    Write-Host "✅ 备份完成: $backupPath" -ForegroundColor Green
} catch {
    Write-Host "❌ 备份失败: $_" -ForegroundColor Red
    exit
}

# 读取文件内容
try {
    $bytes = [System.IO.File]::ReadAllBytes($exePath)
    Write-Host "📁 文件大小: $($bytes.Length) 字节" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 读取文件失败: $_" -ForegroundColor Red
    exit
}

# 转换为字符串进行搜索和替换
$content = [System.Text.Encoding]::UTF8.GetString($bytes)

# 检查是否包含目标字符串
$foundBroker = $content.Contains("broker.mqttdashboard.com")
$foundTopic = $content.Contains("nitamade/jiushe/ge/shabi")

Write-Host "🔍 搜索结果:" -ForegroundColor Yellow
Write-Host "   MQTT服务器地址: $foundBroker" -ForegroundColor $(if($foundBroker){"Green"}else{"Red"})
Write-Host "   MQTT主题: $foundTopic" -ForegroundColor $(if($foundTopic){"Green"}else{"Red"})

if ($foundBroker -or $foundTopic) {
    # 进行替换
    $modifiedContent = $content
    $replacementCount = 0
    
    if ($foundBroker) {
        # 替换服务器地址 - 保持相同长度很重要
        $oldServer = "broker.mqttdashboard.com"
        $newServer = "*************" + "`0" * ($oldServer.Length - "*************".Length)
        $modifiedContent = $modifiedContent.Replace($oldServer, $newServer)
        $replacementCount++
        Write-Host "✅ 替换服务器地址: $oldServer -> *************" -ForegroundColor Green
    }
    
    if ($foundTopic) {
        # 替换主题 - 保持相同长度
        $oldTopic = "nitamade/jiushe/ge/shabi"
        $newTopic = "`$dp" + "`0" * ($oldTopic.Length - "`$dp".Length)
        $modifiedContent = $modifiedContent.Replace($oldTopic, $newTopic)
        $replacementCount++
        Write-Host "✅ 替换MQTT主题: $oldTopic -> `$dp" -ForegroundColor Green
    }
    
    # 转换回字节数组并保存
    try {
        $modifiedBytes = [System.Text.Encoding]::UTF8.GetBytes($modifiedContent)
        [System.IO.File]::WriteAllBytes($outputPath, $modifiedBytes)
        Write-Host "✅ 修改完成: $outputPath" -ForegroundColor Green
        Write-Host "📊 总共替换了 $replacementCount 处配置" -ForegroundColor Cyan
    } catch {
        Write-Host "❌ 保存文件失败: $_" -ForegroundColor Red
        exit
    }
    
} else {
    Write-Host "❌ 没有找到需要替换的配置" -ForegroundColor Red
    exit
}

# 创建配置说明文件
$configInfo = @"
# OneNET配置修改说明

## 修改内容：
1. MQTT服务器地址: broker.mqttdashboard.com -> *************
2. MQTT主题: nitamade/jiushe/ge/shabi -> `$dp

## 注意事项：
1. 端口需要从1883改为6002 (这个需要在STM32代码中修改)
2. 需要配置OneNET的认证信息
3. 数据格式需要符合OneNET的JSON格式

## 您的OneNET配置信息：
- 产品ID: cF16DWy2B8
- 设备ID: stm32f103
- 访问密钥: QU9uYW44bG5pcG1NVkhLWmNPUnNGZXVVcHhDZEtQMkM=

## 下一步：
1. 测试修改后的程序是否能正常运行
2. 确保STM32发送的数据格式正确
3. 验证OneNET平台能接收到数据

## 如果遇到问题：
1. 使用备份文件恢复原程序
2. 检查OneNET平台配置
3. 验证网络连接
"@

$configInfo | Out-File -FilePath "OneNET_Config_Info.txt" -Encoding UTF8
Write-Host "📝 配置说明文件已创建: OneNET_Config_Info.txt" -ForegroundColor Green

Write-Host "`n🎉 修改完成！" -ForegroundColor Green
Write-Host "📁 原文件: $exePath" -ForegroundColor Cyan
Write-Host "📁 备份文件: $backupPath" -ForegroundColor Cyan
Write-Host "📁 修改后文件: $outputPath" -ForegroundColor Cyan

Write-Host "`n⚠️  重要提醒:" -ForegroundColor Yellow
Write-Host "1. 请先测试修改后的程序是否能正常启动" -ForegroundColor White
Write-Host "2. 需要确保STM32代码中的端口改为6002" -ForegroundColor White
Write-Host "3. 数据格式需要改为OneNET的JSON格式" -ForegroundColor White
Write-Host "4. 如有问题，使用备份文件恢复" -ForegroundColor White

Write-Host "`n按任意键继续..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
